import AddIcon from "@mui/icons-material/Add";
import removeUndefined from "../../lib/remove-undefined";
import {
  <PERSON>ert,
  AlertTitle,
  Box,
  Button,
  Stack,
  Typography,
} from "@mui/material";
import { Company } from "../../models/company/company";
import { geoCodeToLocation } from "../../lib/geo-code-to-location";
import { updateDoc } from "firebase/firestore";
import { useState } from "react";
import { geocodeByPlaceId } from "react-google-places-autocomplete";
import GoogleLocationSearch from "../search/GoogleLocationSearch";
import { v4 as uuid } from "uuid";
import MapBox from "../map/MapBox";

const CompanyHomeLocation = ({
  company,
  noWarning,
}: {
  company: Company;
  noWarning?: boolean;
}) => {
  const [geoCode, setGeoCode] = useState<
    google.maps.GeocoderResult | undefined
  >();
  const [noZipCodeError, setNoZipCodeError] = useState(false);
  const [edit, setEdit] = useState(false);

  return (
    <Stack spacing={2}>
      <Stack alignItems="center" direction="row">
        {!noWarning && <Typography variant="h6">Home location </Typography>}

        {!noWarning && <Box flex={1} />}

        <Button
          startIcon={<AddIcon />}
          size="small"
          variant="contained"
          onClick={() => {
            setEdit(!edit);
            setGeoCode(undefined);
          }}
        >
          Add new location
        </Button>
      </Stack>

      {!noWarning && !company.homeLocations?.length && (
        <Alert severity="warning">
          <AlertTitle>No home location set</AlertTitle>
          This location used for calculation distance between work and
          installer.
        </Alert>
      )}

      {edit ? (
        <Stack spacing={2}>
          <GoogleLocationSearch
            onLocationChanged={async (location) => {
              if (!location) {
                setGeoCode(undefined);
                setNoZipCodeError(false);
                return;
              }

              const geoCode = await geocodeByPlaceId(location.place_id);

              setGeoCode(geoCode[0]);

              const postalCode = geoCode[0].address_components.filter((e) =>
                e.types.includes("postal_code"),
              )[0]?.long_name;

              if (!postalCode) {
                setNoZipCodeError(true);
              } else {
                setNoZipCodeError(false);
              }
            }}
          />

          {noZipCodeError && (
            <>
              <Alert style={{ marginTop: 8 }} severity="error">
                No zip code in address
              </Alert>
            </>
          )}

          {geoCode && !noZipCodeError && (
            <MapBox center={geoCode.geometry.location} />
          )}

          <Button
            onClick={async () => {
              const customerLocationData = geoCodeToLocation(geoCode!);

              const homeLocation = removeUndefined({
                type: "premise",
                addressLine: customerLocationData.addressLine,
                coordinates: customerLocationData.coordinates,
                uuid: uuid(),
                postalTown: customerLocationData.postalTown,
              });

              // on inner operations we cannot use removeArray or arrayUnion
              await updateDoc(company.reference, {
                homeLocations: [...(company.homeLocations ?? []), homeLocation],
              });
              setEdit(false);
            }}
            variant="contained"
            disabled={!Boolean(geoCode) || noZipCodeError}
          >
            Add new address
          </Button>
        </Stack>
      ) : null}

      {company.homeLocations?.map((location, index) => {
        return (
          <div key={index} style={{ marginTop: 8, marginBottom: 8 }}>
            {location?.coordinates ? (
              <MapBox
                sx={{ height: 200 }}
                center={{
                  lat: location?.coordinates?.latitude,
                  lng: location?.coordinates?.longitude,
                }}
              />
            ) : null}
            <Typography>
              {location.addressLine}{" "}
              <Button
                onClick={async () => {
                  const newList =
                    company.homeLocations?.filter((_, removedIndex) => {
                      return index !== removedIndex;
                    }) ?? [];

                  // on inner operations we cannot use removeArray or arrayUnion
                  updateDoc(company.reference, {
                    homeLocations: newList,
                  });
                }}
                color="error"
                size="small"
              >
                Remove
              </Button>
            </Typography>
          </div>
        );
      })}
    </Stack>
  );
};

export default CompanyHomeLocation;
