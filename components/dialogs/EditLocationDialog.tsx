import FormDialog from "./FormDialog";
import GoogleLocationSearch from "../search/GoogleLocationSearch";
import Link from "next/link";
import removeUndefined from "../../lib/remove-undefined";
import TextField from "../form-fields/TextField";
import UserDetail from "../user/UserDetail";
import { Button, capitalize, Grid2, Stack } from "@mui/material";
import { CENTRAL_SWEDEN_COORDINATES } from "../../config/constants";
import { DialogProps } from "./default-dialog-props";
import { DocumentReference, GeoPoint, updateDoc } from "firebase/firestore";
import { DoneLocation, fetchUser } from "../../models/user/user";
import { FormSpy, useForm } from "react-final-form";
import { geocodeByPlaceId } from "react-google-places-autocomplete";
import {
  ArrowLeft,
  Map,
  PersonSearch as PersonSearchIcon,
} from "@mui/icons-material";
import MapBox from "../map/MapBox";

interface EditLocationDialogProps extends DialogProps {
  location?: DoneLocation;
  reference: DocumentReference;
  path: string;
  userReference?: DocumentReference;
  jobReference?: DocumentReference;
  userName?: string;
}

export default function EditLocationDialog({
  location,
  reference,
  path,
  isOpen,
  jobReference,
  userReference,
  userName,
  close,
}: EditLocationDialogProps) {
  const raw = location?.addressLine ?? "";
  const [street, postalAndTown, country] = raw.split(", ").map((s) => s.trim());

  let town;
  let zip;

  try {
    town = postalAndTown.split(" ").pop();
    zip = postalAndTown.split(" ").reverse().slice(1).reverse().join(" ");
  } catch {
    console.warn("Parse error on address");
  }

  const initLocation = {
    addressLine: location?.addressLine,
    street,
    zip,
    town,
    country,
    latitude: location?.coordinates?.latitude,
    longitude: location?.coordinates?.longitude,
  };

  return (
    <FormDialog
      maxWidth={userReference ? "xl" : undefined}
      isOpen={isOpen}
      close={close}
      title={"Edit location"}
      initialValues={initLocation}
      onSubmit={async (changes) => {
        const customerLocation = removeUndefined({
          addressLine: [
            changes.street,
            [changes.zip, changes.town].join(" "),
            changes.country,
          ].join(", "),
          zip: changes.zip,
          postalTown: changes.town,
          coordinates:
            changes?.latitude && changes?.longitude
              ? new GeoPoint(changes.latitude, changes.longitude)
              : undefined,
        });

        await updateDoc(reference, path, customerLocation);
      }}
    >
      <Grid2 spacing={2} container>
        <Grid2 size={{ xs: userReference ? 6 : 12 }}>
          <Stack spacing={2}>
            {userName && (
              <Link
                href={`https://www.eniro.se/${userName.replace(
                  " ",
                  "+",
                )}/personer`}
                target="_blank"
              >
                <Button startIcon={<PersonSearchIcon />}>
                  Lookup {userName} on Eniro
                </Button>
              </Link>
            )}

            <FormSpy>
              {(props) => (
                <Link
                  href={`https://kartor.eniro.se/s/${[
                    props.values?.street,
                    props.values?.zip,
                    props.values?.town,
                  ]
                    .filter((e) => e?.length)
                    .map((e) => capitalize(e))
                    .join(" ")
                    .replaceAll(" ", ",%20")}`}
                  target="_blank"
                >
                  <Button startIcon={<Map />}>Lookup address on Eniro</Button>
                </Link>
              )}
            </FormSpy>

            <LocationSearch />

            <TextField name="street" label="Street address line" />
            <TextField name="zip" label="Zip code" />
            <TextField name="town" label="Town/City" />
            <TextField name="country" label="Country" />

            <TextField type="number" name="latitude" label="latitude" />
            <TextField type="number" name="longitude" label="longitude" />

            <FormSpy>
              {(props) => (
                <CoordinatePicker
                  lat={parseFloat(props.values?.latitude)}
                  lng={parseFloat(props.values?.longitude)}
                />
              )}
            </FormSpy>
          </Stack>
        </Grid2>
        {userReference && (
          <Grid2 size={{ xs: userReference ? 6 : 12 }}>
            <UserDetail userId={userReference.id} />
            {jobReference && userReference && (
              <CopyLocationFromUserToJob userReference={userReference} />
            )}
          </Grid2>
        )}
      </Grid2>
    </FormDialog>
  );
}

function CoordinatePicker({ lat, lng }: { lat?: number; lng?: number }) {
  const form = useForm();

  return (
    <MapBox
      sx={{ height: 400 }}
      center={{
        lat: lat ?? CENTRAL_SWEDEN_COORDINATES.lat,
        lng: lng ?? CENTRAL_SWEDEN_COORDINATES.lng,
      }}
    />
  );
}

function LocationSearch() {
  const form = useForm();

  return (
    <GoogleLocationSearch
      onLocationChanged={async (location) => {
        const geoCode = await geocodeByPlaceId(location.place_id);

        const coordinates = geoCode[0].geometry.location;

        const zipCode = geoCode[0].address_components.filter((e) =>
          e.types.includes("postal_code"),
        )[0]?.long_name;

        const country = geoCode[0].address_components.filter((e) =>
          e.types.includes("country"),
        )[0]?.long_name;

        const postalTown = geoCode[0].address_components.filter((e) =>
          e.types.includes("postal_town"),
        )[0]?.long_name;

        const route = geoCode[0].address_components.filter((e) =>
          e.types.includes("route"),
        )[0]?.long_name;

        const streetNumber = geoCode[0].address_components.filter((e) =>
          e.types.includes("street_number"),
        )[0]?.long_name;

        form.batch(() => {
          form.change("latitude", coordinates.lat());
          form.change("longitude", coordinates.lng());
          form.change("zipCode", zipCode);
          form.change("addressLine", geoCode[0].formatted_address);
          form.change("country", country);
          form.change("town", postalTown);
          form.change(
            "street",
            [route, streetNumber].filter((e) => Boolean(e)).join(" "),
          );
        });
      }}
    />
  );
}

function CopyLocationFromUserToJob({
  userReference,
}: {
  userReference: DocumentReference;
}) {
  const form = useForm();

  return (
    <Button
      onClick={async () => {
        const user = await fetchUser(userReference);

        const raw = user?.location?.addressLine ?? "";
        const [street, postalAndTown, country] = raw
          .split(", ")
          .map((s) => s.trim());

        let town;
        let zip;

        try {
          town = postalAndTown.split(" ").pop();
          zip = postalAndTown.split(" ").reverse().slice(1).reverse().join(" ");
        } catch {
          console.warn("Parse error on address");
        }

        const initLocation = {
          addressLine: user?.location?.addressLine,
          street,
          zip,
          town,
          country,
          latitude: user?.location?.coordinates?.latitude,
          longitude: user?.location?.coordinates?.longitude,
        };

        form.batch(() => {
          form.change("latitude", initLocation.latitude);
          form.change("longitude", initLocation.longitude);
          form.change("zipCode", initLocation.zip);
          form.change("addressLine", initLocation.addressLine);
          form.change("country", initLocation.country);
          form.change("town", initLocation.town);
          form.change("street", initLocation.street);
        });
      }}
      startIcon={<ArrowLeft />}
    >
      Copy location from user to job
    </Button>
  );
}
