import * as React from "react";
import TextField from "@mui/material/TextField";
import { debounce } from "@mui/material/utils";
import Search from "@mui/icons-material/Search";
import { Autocomplete, Grid2, InputAdornment, Typography } from "@mui/material";
import { useState } from "react";
import { meiliSearch } from "../../lib/services/meili-search";
import {
  globalSearch,
  SearchIndexes,
  SearchData,
} from "../../lib/services/global-search";
import { useAuthUser } from "../auth/AuthProvider";
import { Field } from "react-final-form";

/**
 * @deprecated Use `GlobalSearchField` or `GlobalSearchAutocomplete` instead.
 */
export default function GlobalSearchField({
  input,
  label,
  onSearchResultChanged,
  searchOptions,
  icon = <Search />,
}: {
  icon?: React.ReactNode;
  input?: string;
  label: string;
  searchOptions: SearchIndexes[];
  onSearchResultChanged: (location: SearchData) => void;
}) {
  const searchEngine = meiliSearch();
  const authUser = useAuthUser();
  const [value, setValue] = useState<SearchData | null>(null);
  const [searchQuery, setSearchQuery] = useState(input);
  const [options, setOptions] = useState<readonly SearchData[]>([]);
  const [open, setOpen] = React.useState(false);

  const fetch = React.useMemo(
    () =>
      debounce(
        (
          request: { input: string },
          callback: (results?: readonly SearchData[]) => void,
        ) => {
          globalSearch(request.input, searchOptions, authUser.partner).then(
            (results) => {
              callback(results);
            },
          );
        },
        400,
      ),
    [searchEngine, authUser.partner, searchOptions],
  );

  React.useEffect(() => {
    let active = true;

    if (searchQuery === "") {
      setOptions(value ? [value] : []);
      return undefined;
    }

    fetch({ input: searchQuery ?? "" }, (results?: readonly SearchData[]) => {
      if (active) {
        let newOptions: readonly SearchData[] = [];

        if (value) {
          newOptions = [value];
        }

        if (results) {
          newOptions = [...newOptions, ...results];
        }

        setOptions(newOptions);
      }
    });

    return () => {
      active = false;
    };
  }, [value, searchQuery, fetch]);

  return (
    <Autocomplete
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.title
      }
      filterOptions={(x) => x}
      options={options}
      open={open}
      onClose={() => setOpen(false)}
      noOptionsText={false}
      autoComplete
      includeInputInList
      filterSelectedOptions
      value={value}
      onChange={(_, newValue: SearchData | null) => {
        setOptions(newValue ? [newValue, ...options] : options);
        setValue(newValue);
        if (newValue) {
          onSearchResultChanged(newValue);
        }
      }}
      onInputChange={(_, newInputValue) => {
        if (newInputValue.length === 0) {
          if (open) setOpen(false);
        } else {
          if (!open) setOpen(true);
        }
        setSearchQuery(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          sx={{ backgroundColor: "background.default", borderRadius: 1 }}
          slotProps={{
            input: {
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">{icon}</InputAdornment>
              ),
              disableUnderline: true,
            },
          }}
          placeholder={label}
          fullWidth
        />
      )}
      renderOption={(props, option) => {
        return (
          <li {...props}>
            <Grid2 container alignItems="center">
              <Grid2
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
              >
                <Typography sx={{ fontWeight: "bold" }}>
                  {option.title}
                </Typography>

                <Typography color="text.secondary" variant="body2">
                  {option.type === "users" && option.payload.phoneNumber}
                  {option.type === "companies" &&
                    option.payload.services?.join(", ")}
                </Typography>
              </Grid2>
            </Grid2>
          </li>
        );
      }}
    />
  );
}

interface GlobalSearchFormFieldProps {
  icon?: React.ReactNode;
  name: string;
  label: string;
  searchOptions: SearchIndexes[];
}

export function GlobalSearchFormField({
  icon,
  name,
  label,
  searchOptions,
}: GlobalSearchFormFieldProps) {
  return (
    <Field
      name={name}
      render={({ input }) => (
        <GlobalSearchField
          icon={icon}
          label={label}
          searchOptions={searchOptions}
          input={input.value.id as string}
          onSearchResultChanged={(result) => {
            input.onChange(result.reference);
          }}
        />
      )}
    />
  );
}
